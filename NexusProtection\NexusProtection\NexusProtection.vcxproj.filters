<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Core">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{2DAB880B-99B4-887C-2230-9F7C8E38947C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules">
      <UniqueIdentifier>{3DAB880B-99B4-887C-2230-9F7C8E38947C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\AntiDupe">
      <UniqueIdentifier>{5DAB880B-99B4-887C-2230-9F7C8E38947C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Libraries">
      <UniqueIdentifier>{4DAB880B-99B4-887C-2230-9F7C8E38947C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Core\NexusCore.h">
      <Filter>Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\ModuleManager.h">
      <Filter>Core</Filter>
    </ClInclude>
    <ClInclude Include="Common\IModule.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ConfigManager.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Logger.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Utils.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Singleton.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Modules\AntiDupe\AntiDupeModule.h">
      <Filter>Modules\AntiDupe</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\NexusCore.cpp">
      <Filter>Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\ModuleManager.cpp">
      <Filter>Core</Filter>
    </ClCompile>
    <ClCompile Include="Common\ConfigManager.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Logger.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Utils.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Modules\AntiDupe\AntiDupeModule.cpp">
      <Filter>Modules\AntiDupe</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
