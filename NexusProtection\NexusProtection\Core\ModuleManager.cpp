#include "../pch.h"
#include "ModuleManager.h"
#include "../Common/Utils.h"
#include "../Common/Logger.h"
#include "../Common/ConfigManager.h"
#include "../Modules/AntiDupe/AntiDupeModule.h"

namespace NexusProtection
{
    void ModuleManager::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        if (m_initialized)
            return;

        NEXUS_LOG_INFO("Initializing Module Manager");
        
        // Create module instances
        CreateModuleInstances();
        
        m_lastUpdate = std::chrono::steady_clock::now();
        m_initialized = true;
        
        NEXUS_LOG_INFO("Module Manager initialized with " + std::to_string(m_modules.size()) + " modules");
    }

    void ModuleManager::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        if (!m_initialized)
            return;

        NEXUS_LOG_INFO("Shutting down Module Manager");
        
        // Stop and shutdown all modules
        if (m_modulesStarted)
        {
            StopModules();
        }
        ShutdownModules();
        
        m_modules.clear();
        m_initialized = false;
        m_modulesStarted = false;
        
        NEXUS_LOG_INFO("Module Manager shutdown complete");
    }

    bool ModuleManager::RegisterModule(ModulePtr module)
    {
        if (!module || !ValidateModule(module))
        {
            NEXUS_LOG_ERROR("Failed to register module: Invalid module");
            return false;
        }

        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        const std::string& name = module->GetName();
        if (m_modules.find(name) != m_modules.end())
        {
            NEXUS_LOG_WARNING("Module already registered: " + name);
            return false;
        }

        m_modules[name] = module;
        NEXUS_LOG_INFO("Registered module: " + name);
        return true;
    }

    bool ModuleManager::UnregisterModule(const std::string& name)
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        auto it = m_modules.find(name);
        if (it == m_modules.end())
        {
            NEXUS_LOG_WARNING("Module not found for unregistration: " + name);
            return false;
        }

        // Stop and shutdown the module if it's running
        ModulePtr module = it->second;
        if (module->GetState() == ModuleState::Running)
        {
            module->Stop();
        }
        if (module->GetState() != ModuleState::Unloaded)
        {
            module->Shutdown();
        }

        m_modules.erase(it);
        NEXUS_LOG_INFO("Unregistered module: " + name);
        return true;
    }

    bool ModuleManager::InitializeModules()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        NEXUS_LOG_INFO("Initializing modules...");
        
        bool allSuccess = true;
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            
            if (!module->IsEnabled())
            {
                NEXUS_LOG_INFO("Skipping disabled module: " + name);
                continue;
            }
            
            NEXUS_LOG_INFO("Initializing module: " + name);
            
            if (!module->Initialize())
            {
                NEXUS_LOG_ERROR("Failed to initialize module: " + name + " - " + module->GetLastError());
                allSuccess = false;
                continue;
            }
            
            NEXUS_LOG_INFO("Module initialized successfully: " + name);
        }
        
        return allSuccess;
    }

    bool ModuleManager::StartModules()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        if (m_modulesStarted)
            return true;

        NEXUS_LOG_INFO("Starting modules...");
        
        bool allSuccess = true;
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            
            if (!module->IsEnabled() || module->GetState() != ModuleState::Loaded)
            {
                continue;
            }
            
            NEXUS_LOG_INFO("Starting module: " + name);
            
            if (!module->Start())
            {
                NEXUS_LOG_ERROR("Failed to start module: " + name + " - " + module->GetLastError());
                allSuccess = false;
                continue;
            }
            
            NEXUS_LOG_INFO("Module started successfully: " + name);
        }
        
        m_modulesStarted = allSuccess;
        return allSuccess;
    }

    void ModuleManager::StopModules()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        if (!m_modulesStarted)
            return;

        NEXUS_LOG_INFO("Stopping modules...");
        
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            
            if (module->GetState() == ModuleState::Running)
            {
                NEXUS_LOG_INFO("Stopping module: " + name);
                module->Stop();
            }
        }
        
        m_modulesStarted = false;
    }

    void ModuleManager::ShutdownModules()
    {
        // Note: This method is called from Shutdown() which already holds the lock
        
        NEXUS_LOG_INFO("Shutting down modules...");
        
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            
            if (module->GetState() != ModuleState::Unloaded)
            {
                NEXUS_LOG_INFO("Shutting down module: " + name);
                module->Shutdown();
            }
        }
    }

    ModulePtr ModuleManager::GetModule(const std::string& name) const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        auto it = m_modules.find(name);
        return (it != m_modules.end()) ? it->second : nullptr;
    }

    std::vector<ModulePtr> ModuleManager::GetAllModules() const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        std::vector<ModulePtr> modules;
        for (const auto& pair : m_modules)
        {
            modules.push_back(pair.second);
        }
        
        return modules;
    }

    std::vector<std::string> ModuleManager::GetModuleNames() const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        std::vector<std::string> names;
        for (const auto& pair : m_modules)
        {
            names.push_back(pair.first);
        }
        
        return names;
    }

    size_t ModuleManager::GetModuleCount() const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        return m_modules.size();
    }

    size_t ModuleManager::GetActiveModuleCount() const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        size_t count = 0;
        for (const auto& pair : m_modules)
        {
            if (pair.second->GetState() == ModuleState::Running)
            {
                count++;
            }
        }
        
        return count;
    }

    bool ModuleManager::IsModuleLoaded(const std::string& name) const
    {
        ModulePtr module = GetModule(name);
        return module && (module->GetState() == ModuleState::Loaded || module->GetState() == ModuleState::Running);
    }

    bool ModuleManager::IsModuleRunning(const std::string& name) const
    {
        ModulePtr module = GetModule(name);
        return module && module->GetState() == ModuleState::Running;
    }

    void ModuleManager::UpdateModules()
    {
        auto now = std::chrono::steady_clock::now();
        if (now - m_lastUpdate < m_updateInterval)
            return;

        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            if (module->GetState() == ModuleState::Running)
            {
                try
                {
                    module->Update();
                }
                catch (const std::exception& e)
                {
                    NEXUS_LOG_ERROR("Exception in module update: " + module->GetName() + " - " + e.what());
                }
            }
        }
        
        m_lastUpdate = now;
    }

    void ModuleManager::OnZoneStart()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        NEXUS_LOG_INFO("Notifying modules of zone start");
        
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            if (module->GetState() == ModuleState::Running)
            {
                try
                {
                    module->OnZoneStart();
                }
                catch (const std::exception& e)
                {
                    NEXUS_LOG_ERROR("Exception in module OnZoneStart: " + module->GetName() + " - " + e.what());
                }
            }
        }
    }

    bool ModuleManager::LoadModuleConfigs()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        bool allSuccess = true;
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            std::string configPath = Utils::GetConfigPath(name);
            
            if (!module->LoadConfig(configPath))
            {
                NEXUS_LOG_WARNING("Failed to load config for module: " + name);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }

    bool ModuleManager::SaveModuleConfigs()
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        bool allSuccess = true;
        for (auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            const std::string& name = module->GetName();
            std::string configPath = Utils::GetConfigPath(name);
            
            if (!module->SaveConfig(configPath))
            {
                NEXUS_LOG_WARNING("Failed to save config for module: " + name);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }

    std::vector<std::string> ModuleManager::GetModuleErrors() const
    {
        std::lock_guard<std::mutex> lock(m_modulesMutex);
        
        std::vector<std::string> errors;
        for (const auto& pair : m_modules)
        {
            ModulePtr module = pair.second;
            if (module->GetState() == ModuleState::Error)
            {
                errors.push_back(module->GetName() + ": " + module->GetLastError());
            }
        }
        
        return errors;
    }

    void ModuleManager::CreateModuleInstances()
    {
        NEXUS_LOG_INFO("Creating module instances...");

        // Register all protection modules
        try
        {
            // Anti-Dupe Module
            RegisterModule(std::make_shared<Modules::AntiDupeModule>());

            // TODO: Add more modules here as they are created
            // RegisterModule(std::make_shared<Modules::AntiCheatModule>());
            // RegisterModule(std::make_shared<Modules::AntiSpeedHackModule>());
            // RegisterModule(std::make_shared<Modules::AntiOverdamageModule>());
            // etc.

            NEXUS_LOG_INFO("Module instances created successfully");
        }
        catch (const std::exception& e)
        {
            NEXUS_LOG_ERROR("Failed to create module instances: " + std::string(e.what()));
        }
    }

    bool ModuleManager::ValidateModule(ModulePtr module) const
    {
        if (!module)
            return false;
        
        // Check if module has a valid name
        std::string name = module->GetName();
        if (name.empty())
            return false;
        
        // Additional validation can be added here
        return true;
    }
}
