#include "../pch.h"
#include "NexusCore.h"
#include "ModuleManager.h"
#include "../Common/Logger.h"
#include "../Common/ConfigManager.h"
#include "../Common/Utils.h"
#include <process.h>

namespace NexusProtection
{
    void NexusCore::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_stateMutex);
        
        if (m_initialized)
            return;

        try
        {
            // Create configuration directories
            if (!CreateConfigDirectories())
            {
                throw std::runtime_error("Failed to create configuration directories");
            }

            // Initialize subsystems
            if (!InitializeSubsystems())
            {
                throw std::runtime_error("Failed to initialize subsystems");
            }

            // Load global configuration
            LoadGlobalConfig();

            m_initialized = true;
            NEXUS_LOG_INFO("NexusCore initialized successfully");
        }
        catch (const std::exception& e)
        {
            NEXUS_LOG_ERROR("Failed to initialize NexusCore: " + std::string(e.what()));
            ShutdownSubsystems();
            throw;
        }
    }

    void NexusCore::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_stateMutex);
        
        if (!m_initialized)
            return;

        NEXUS_LOG_INFO("Shutting down NexusCore");
        
        // Stop execution if running
        if (m_running)
        {
            Stop();
        }

        // Shutdown subsystems
        ShutdownSubsystems();
        
        m_initialized = false;
        NEXUS_LOG_INFO("NexusCore shutdown complete");
    }

    void NexusCore::Start()
    {
        std::unique_lock<std::mutex> lock(m_stateMutex);
        
        if (!m_initialized)
        {
            NEXUS_LOG_ERROR("Cannot start NexusCore: Not initialized");
            return;
        }
        
        if (m_running)
        {
            NEXUS_LOG_WARNING("NexusCore is already running");
            return;
        }

        NEXUS_LOG_INFO("Starting NexusCore");
        
        m_stopRequested = false;
        m_running = true;
        
        // Start execution thread
        m_executionThread = reinterpret_cast<HANDLE>(_beginthread(ExecutionThread, 0, this));
        if (m_executionThread == nullptr)
        {
            m_running = false;
            NEXUS_LOG_ERROR("Failed to create execution thread");
            return;
        }
        
        NEXUS_LOG_INFO("NexusCore started successfully");
    }

    void NexusCore::Stop()
    {
        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            
            if (!m_running)
                return;

            NEXUS_LOG_INFO("Stopping NexusCore");
            m_stopRequested = true;
        }
        
        // Notify condition variable to wake up waiting threads
        m_stateCondition.notify_all();
        
        // Wait for execution thread to finish
        if (m_executionThread != nullptr)
        {
            WaitForSingleObject(m_executionThread, 5000); // 5 second timeout
            CloseHandle(m_executionThread);
            m_executionThread = nullptr;
        }
        
        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_running = false;
        }
        
        NEXUS_LOG_INFO("NexusCore stopped");
    }

    void NexusCore::OnZoneServerStart()
    {
        NEXUS_LOG_INFO("Zone server started - notifying modules");
        m_zoneServerStarted = true;
        
        // Notify modules
        ModuleManager::GetInstance().OnZoneStart();
        
        // Wake up execution thread if it's waiting
        m_stateCondition.notify_all();
    }

    void NexusCore::OnZoneServerStop()
    {
        NEXUS_LOG_INFO("Zone server stopped");
        m_zoneServerStarted = false;
    }

    bool NexusCore::LoadGlobalConfig()
    {
        ConfigManager& config = ConfigManager::GetInstance();
        std::string configPath = Utils::GetModuleDirectory() + "NexusPro-Config/global.ini";
        
        if (!Utils::FileExists(configPath))
        {
            NEXUS_LOG_INFO("Global config file not found, creating default configuration");
            LoadDefaultConfig();
            return SaveGlobalConfig();
        }
        
        if (!config.LoadConfig(configPath))
        {
            NEXUS_LOG_ERROR("Failed to load global configuration");
            return false;
        }
        
        // Apply configuration settings
        m_updateInterval = std::chrono::milliseconds(
            config.GetInt("Core", "UpdateInterval", 10));
        m_zoneWaitTimeout = std::chrono::milliseconds(
            config.GetInt("Core", "ZoneWaitTimeout", 5000));
        
        NEXUS_LOG_INFO("Global configuration loaded successfully");
        return true;
    }

    bool NexusCore::SaveGlobalConfig()
    {
        ConfigManager& config = ConfigManager::GetInstance();
        std::string configPath = Utils::GetModuleDirectory() + "NexusPro-Config/global.ini";
        
        // Set current configuration values
        config.SetInt("Core", "UpdateInterval", static_cast<int>(m_updateInterval.count()));
        config.SetInt("Core", "ZoneWaitTimeout", static_cast<int>(m_zoneWaitTimeout.count()));
        
        if (!config.SaveConfig(configPath))
        {
            NEXUS_LOG_ERROR("Failed to save global configuration");
            return false;
        }
        
        return true;
    }

    void NexusCore::ExecutionThread(void* param)
    {
        NexusCore* core = static_cast<NexusCore*>(param);
        if (core)
        {
            core->ExecutionLoop();
        }
        _endthread();
    }

    void NexusCore::ExecutionLoop()
    {
        NEXUS_LOG_INFO("NexusCore execution loop started");
        
        // Wait for zone server to start or timeout
        {
            std::unique_lock<std::mutex> lock(m_stateMutex);
            m_stateCondition.wait_for(lock, m_zoneWaitTimeout, [this] {
                return m_zoneServerStarted.load() || m_stopRequested.load();
            });
        }
        
        if (m_stopRequested)
        {
            NEXUS_LOG_INFO("Stop requested before zone server start");
            return;
        }
        
        if (!m_zoneServerStarted)
        {
            NEXUS_LOG_WARNING("Zone server did not start within timeout period");
        }
        
        // Main execution loop
        auto lastUpdate = std::chrono::steady_clock::now();
        
        while (!m_stopRequested)
        {
            auto now = std::chrono::steady_clock::now();
            
            // Update modules at specified interval
            if (now - lastUpdate >= m_updateInterval)
            {
                try
                {
                    ModuleManager::GetInstance().UpdateModules();
                    lastUpdate = now;
                }
                catch (const std::exception& e)
                {
                    NEXUS_LOG_ERROR("Exception in module update loop: " + std::string(e.what()));
                }
            }
            
            // Small sleep to prevent excessive CPU usage
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        NEXUS_LOG_INFO("NexusCore execution loop ended");
    }

    bool NexusCore::InitializeSubsystems()
    {
        try
        {
            // Initialize logger first
            Logger::GetInstance().Initialize(Utils::GetLogPath());
            NEXUS_LOG_INFO("Logger initialized");
            
            // Initialize configuration manager
            ConfigManager::GetInstance().Initialize();
            NEXUS_LOG_INFO("Configuration manager initialized");
            
            // Initialize module manager
            ModuleManager::GetInstance().Initialize();
            NEXUS_LOG_INFO("Module manager initialized");
            
            // Load module configurations
            ModuleManager::GetInstance().LoadModuleConfigs();
            
            // Initialize modules
            if (!ModuleManager::GetInstance().InitializeModules())
            {
                NEXUS_LOG_WARNING("Some modules failed to initialize");
            }
            
            // Start modules
            if (!ModuleManager::GetInstance().StartModules())
            {
                NEXUS_LOG_WARNING("Some modules failed to start");
            }
            
            return true;
        }
        catch (const std::exception& e)
        {
            NEXUS_LOG_ERROR("Exception during subsystem initialization: " + std::string(e.what()));
            return false;
        }
    }

    void NexusCore::ShutdownSubsystems()
    {
        try
        {
            // Shutdown in reverse order
            ModuleManager::GetInstance().Shutdown();
            ConfigManager::GetInstance().Shutdown();
            Logger::GetInstance().Shutdown();
        }
        catch (const std::exception& e)
        {
            // Can't log here as logger might be shut down
            OutputDebugStringA(("Exception during subsystem shutdown: " + std::string(e.what())).c_str());
        }
    }

    bool NexusCore::CreateConfigDirectories()
    {
        std::string baseDir = Utils::GetModuleDirectory() + "NexusPro-Config/";
        std::string logsDir = baseDir + "logs/";
        
        return Utils::CreateDirectoryRecursive(baseDir) && 
               Utils::CreateDirectoryRecursive(logsDir);
    }

    void NexusCore::LoadDefaultConfig()
    {
        ConfigManager& config = ConfigManager::GetInstance();
        
        // Core settings
        config.SetInt("Core", "UpdateInterval", 10);
        config.SetInt("Core", "ZoneWaitTimeout", 5000);
        config.SetBool("Core", "EnableLogging", true);
        config.SetString("Core", "LogLevel", "Info");
        
        // Default module settings will be added here as modules are created
    }
}
