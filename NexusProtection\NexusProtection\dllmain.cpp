#include "pch.h"
#include "Core/NexusCore.h"
#include "Common/Logger.h"

// Global instance pointer for cleanup
static NexusProtection::NexusCore* g_pNexusCore = nullptr;

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    UNREFERENCED_PARAMETER(hModule);
    UNREFERENCED_PARAMETER(lpReserved);

    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            // Disable thread library calls for performance
            DisableThreadLibraryCalls(hModule);
            
            try
            {
                // Get NexusCore instance and initialize
                g_pNexusCore = &NexusProtection::NexusCore::GetInstance();
                g_pNexusCore->Initialize();
                g_pNexusCore->Start();
                
                // Note: We don't call OnZoneServerStart here because we need to detect
                // when the zone server is actually ready. This will be handled by
                // hooking into the zone server's initialization process.
            }
            catch (const std::exception& e)
            {
                // Try to log the error, but logger might not be initialized
                try
                {
                    NEXUS_LOG_ERROR("Failed to initialize NexusProtection: " + std::string(e.what()));
                }
                catch (...)
                {
                    // Fallback to debug output
                    OutputDebugStringA(("NexusProtection initialization failed: " + std::string(e.what())).c_str());
                }
                return FALSE;
            }
            catch (...)
            {
                OutputDebugStringA("NexusProtection initialization failed with unknown exception");
                return FALSE;
            }
        }
        break;

    case DLL_PROCESS_DETACH:
        {
            try
            {
                if (g_pNexusCore)
                {
                    g_pNexusCore->Stop();
                    g_pNexusCore->Shutdown();
                    g_pNexusCore = nullptr;
                }
            }
            catch (const std::exception& e)
            {
                try
                {
                    NEXUS_LOG_ERROR("Error during NexusProtection shutdown: " + std::string(e.what()));
                }
                catch (...)
                {
                    OutputDebugStringA(("NexusProtection shutdown error: " + std::string(e.what())).c_str());
                }
            }
            catch (...)
            {
                OutputDebugStringA("NexusProtection shutdown failed with unknown exception");
            }
        }
        break;

    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        // We don't need to handle these for our use case
        break;
    }

    return TRUE;
}

// Export functions for manual loading if needed
extern "C" 
{
    __declspec(dllexport) BOOL InitializeNexusProtection()
    {
        try
        {
            if (!g_pNexusCore)
            {
                g_pNexusCore = &NexusProtection::NexusCore::GetInstance();
                g_pNexusCore->Initialize();
            }
            
            if (!g_pNexusCore->IsRunning())
            {
                g_pNexusCore->Start();
            }
            
            return TRUE;
        }
        catch (...)
        {
            return FALSE;
        }
    }

    __declspec(dllexport) BOOL ShutdownNexusProtection()
    {
        try
        {
            if (g_pNexusCore)
            {
                g_pNexusCore->Stop();
                g_pNexusCore->Shutdown();
                g_pNexusCore = nullptr;
            }
            
            return TRUE;
        }
        catch (...)
        {
            return FALSE;
        }
    }

    __declspec(dllexport) BOOL IsNexusProtectionRunning()
    {
        return g_pNexusCore && g_pNexusCore->IsRunning();
    }

    __declspec(dllexport) void NotifyZoneServerStart()
    {
        if (g_pNexusCore)
        {
            g_pNexusCore->OnZoneServerStart();
        }
    }

    __declspec(dllexport) void NotifyZoneServerStop()
    {
        if (g_pNexusCore)
        {
            g_pNexusCore->OnZoneServerStop();
        }
    }
}
