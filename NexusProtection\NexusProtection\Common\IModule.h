#pragma once
#include "../pch.h"

namespace NexusProtection
{
    enum class ModuleState
    {
        Unloaded,
        Loading,
        Loaded,
        Running,
        Stopping,
        Error
    };

    class IModule
    {
    public:
        virtual ~IModule() = default;

        // Core module lifecycle
        virtual bool Initialize() = 0;
        virtual bool Start() = 0;
        virtual void Stop() = 0;
        virtual void Shutdown() = 0;

        // Module information
        virtual std::string GetName() const = 0;
        virtual std::string GetVersion() const = 0;
        virtual std::string GetDescription() const = 0;

        // Configuration
        virtual bool LoadConfig(const std::string& configPath) = 0;
        virtual bool SaveConfig(const std::string& configPath) = 0;

        // Runtime operations
        virtual void Update() {}
        virtual void OnZoneStart() {}

        // State management
        virtual ModuleState GetState() const { return m_state; }
        virtual bool IsEnabled() const { return m_enabled; }
        virtual void SetEnabled(bool enabled) { m_enabled = enabled; }

        // Error handling
        virtual std::string GetLastError() const { return m_lastError; }

    protected:
        ModuleState m_state = ModuleState::Unloaded;
        bool m_enabled = true;
        std::string m_lastError;

        void SetState(ModuleState state) { m_state = state; }
        void SetError(const std::string& error) 
        { 
            m_lastError = error; 
            m_state = ModuleState::Error;
        }
    };

    using ModulePtr = std::shared_ptr<IModule>;
}
