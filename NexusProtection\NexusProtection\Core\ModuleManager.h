#pragma once
#include "../pch.h"
#include "../Common/IModule.h"
#include "../Common/Singleton.h"

namespace NexusProtection
{
    class ModuleManager : public Singleton<ModuleManager>
    {
    public:
        void Initialize();
        void Shutdown();

        // Module registration
        bool RegisterModule(ModulePtr module);
        bool UnregisterModule(const std::string& name);
        
        // Module lifecycle management
        bool InitializeModules();
        bool StartModules();
        void StopModules();
        void ShutdownModules();
        
        // Module access
        ModulePtr GetModule(const std::string& name) const;
        std::vector<ModulePtr> GetAllModules() const;
        std::vector<std::string> GetModuleNames() const;
        
        // Module state queries
        size_t GetModuleCount() const;
        size_t GetActiveModuleCount() const;
        bool IsModuleLoaded(const std::string& name) const;
        bool IsModuleRunning(const std::string& name) const;
        
        // Runtime operations
        void UpdateModules();
        void OnZoneStart();
        
        // Configuration
        bool LoadModuleConfigs();
        bool SaveModuleConfigs();
        
        // Error handling
        std::vector<std::string> GetModuleErrors() const;

    private:
        friend class Singleton<ModuleManager>;
        ModuleManager() = default;
        ~ModuleManager() = default;

        std::unordered_map<std::string, ModulePtr> m_modules;
        mutable std::mutex m_modulesMutex;
        bool m_initialized = false;
        bool m_modulesStarted = false;
        
        std::chrono::steady_clock::time_point m_lastUpdate;
        std::chrono::milliseconds m_updateInterval{10}; // 10ms update interval
        
        void CreateModuleInstances();
        bool ValidateModule(ModulePtr module) const;
    };
}
