#include "../pch.h"
#include "Logger.h"
#include "Utils.h"

namespace NexusProtection
{
    void Logger::Initialize(const std::string& logPath)
    {
        std::lock_guard<std::mutex> lock(m_logMutex);
        
        if (m_initialized)
            return;

        m_logPath = logPath;
        
        // Create log directory if it doesn't exist
        Utils::CreateDirectoryRecursive(m_logPath);
        
        if (m_fileOutput)
        {
            std::string logFileName = m_logPath + "NexusProtection_" + 
                                    Utils::GetCurrentDateTimeString() + ".log";
            m_logFile.open(logFileName, std::ios::app);
        }
        
        m_initialized = true;
        LogInfo("NexusProtection Logger initialized");
    }

    void Logger::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_logMutex);
        
        if (!m_initialized)
            return;
            
        LogInfo("NexusProtection Logger shutting down");
        
        if (m_logFile.is_open())
        {
            m_logFile.close();
        }
        
        m_initialized = false;
    }

    void Logger::LogDebug(const std::string& message)
    {
        WriteLog(LogLevel::Debug, message);
    }

    void Logger::LogInfo(const std::string& message)
    {
        WriteLog(LogLevel::Info, message);
    }

    void Logger::LogWarning(const std::string& message)
    {
        WriteLog(LogLevel::Warning, message);
    }

    void Logger::LogError(const std::string& message)
    {
        WriteLog(LogLevel::Error, message);
    }

    void Logger::WriteLog(LogLevel level, const std::string& message)
    {
        if (level < m_logLevel || !m_initialized)
            return;

        std::lock_guard<std::mutex> lock(m_logMutex);
        
        std::string logEntry = "[" + GetTimestamp() + "] [" + 
                              LogLevelToString(level) + "] " + message;

        if (m_consoleOutput)
        {
            std::cout << logEntry << std::endl;
        }

        if (m_fileOutput && m_logFile.is_open())
        {
            m_logFile << logEntry << std::endl;
            m_logFile.flush();
        }
    }

    std::string Logger::GetTimestamp() const
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }

    std::string Logger::LogLevelToString(LogLevel level) const
    {
        switch (level)
        {
        case LogLevel::Debug:   return "DEBUG";
        case LogLevel::Info:    return "INFO ";
        case LogLevel::Warning: return "WARN ";
        case LogLevel::Error:   return "ERROR";
        default:                return "UNKN ";
        }
    }
}
