#pragma once

// Windows Headers
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>

// Standard C++ Headers
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <functional>
#include <exception>
#include <stdexcept>
#include <cassert>
#include <cstdint>

// Project-specific defines
#ifdef NEXUSPROTECTION_EXPORTS
#define NEXUS_API __declspec(dllexport)
#else
#define NEXUS_API __declspec(dllimport)
#endif

// Common macros
#define NEXUS_SAFE_DELETE(p) { if(p) { delete (p); (p) = nullptr; } }
#define NEXUS_SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p); (p) = nullptr; } }
#define NEXUS_SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }

// Logging macros
#define NEXUS_LOG_INFO(msg) NexusProtection::Logger::GetInstance().LogInfo(msg)
#define NEXUS_LOG_WARNING(msg) NexusProtection::Logger::GetInstance().LogWarning(msg)
#define NEXUS_LOG_ERROR(msg) NexusProtection::Logger::GetInstance().LogError(msg)
#define NEXUS_LOG_DEBUG(msg) NexusProtection::Logger::GetInstance().LogDebug(msg)

// Namespace
namespace NexusProtection
{
    // Forward declarations
    class NexusCore;
    class ModuleManager;
    class ConfigManager;
    class Logger;
    class IModule;
}
