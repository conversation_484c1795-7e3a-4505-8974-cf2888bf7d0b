#pragma once
#include "../pch.h"
#include "../Common/Singleton.h"

namespace NexusProtection
{
    class NexusCore : public Singleton<NexusCore>
    {
    public:
        void Initialize();
        void Shutdown();
        
        // Main execution loop
        void Start();
        void Stop();
        
        // State queries
        bool IsInitialized() const { return m_initialized; }
        bool IsRunning() const { return m_running; }
        
        // Zone server integration
        void OnZoneServerStart();
        void OnZoneServerStop();
        
        // Configuration
        bool LoadGlobalConfig();
        bool SaveGlobalConfig();

    private:
        friend class Singleton<NexusCore>;
        NexusCore() = default;
        ~NexusCore() = default;

        // Core execution thread
        static void ExecutionThread(void* param);
        void ExecutionLoop();
        
        // Initialization helpers
        bool InitializeSubsystems();
        void ShutdownSubsystems();
        bool CreateConfigDirectories();
        
        // Configuration
        void LoadDefaultConfig();
        
        // State management
        std::atomic<bool> m_initialized{false};
        std::atomic<bool> m_running{false};
        std::atomic<bool> m_stopRequested{false};
        
        // Threading
        HANDLE m_executionThread = nullptr;
        std::mutex m_stateMutex;
        std::condition_variable m_stateCondition;
        
        // Timing
        std::chrono::milliseconds m_updateInterval{10};
        std::chrono::milliseconds m_zoneWaitTimeout{5000};
        
        // Zone server state
        std::atomic<bool> m_zoneServerStarted{false};
    };
}
