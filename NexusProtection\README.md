# NexusPro - Advanced RFOnline Server Protection

NexusPro is a modernized, comprehensive security and anti-cheat system for RFOnline private servers, redesigned from the ground up with Visual Studio 2022 support and a unified DLL architecture.

## Features

### Core Protection Modules
- **AntiDupe Module**: Prevents item duplication exploits including coordinate, trade, auction, mail, box, quest, and money duplication
- **AntiCheat Module**: Detects and prevents various cheating methods (planned)
- **AntiSpeedHack Module**: Prevents speed, fly, and wall hacking (planned)
- **AntiOverdamage Module**: Prevents damage calculation exploits (planned)
- **Memory Protection Module**: Applies server memory patches and protections (planned)

### Architecture Benefits
- **Single DLL**: All protection modules are contained within NexusProtection.dll
- **Modular Design**: Each protection system is a separate internal module with its own configuration
- **Modern C++**: Built with C++17 and Visual Studio 2022
- **Configuration Management**: Each module has its own .ini configuration file
- **Comprehensive Logging**: Detailed logging system with multiple log levels
- **Thread-Safe**: All modules are designed to be thread-safe

## Installation

1. **Build the Project**:
   - Open `NexusPro.sln` in Visual Studio 2022
   - Build in Release x64 configuration
   - The output will be `NexusProtection.dll`

2. **Deploy to Server**:
   - Copy `NexusProtection.dll` to the same directory as `ZoneServer.exe`
   - The DLL will automatically create the `NexusPro-Config` folder on first run

3. **Configuration**:
   - Configuration files will be automatically created in `NexusPro-Config/`
   - Each module has its own `.ini` file (e.g., `AntiDupe.ini`, `AntiCheat.ini`)
   - Global settings are stored in `global.ini`

## Configuration

### Global Configuration (`NexusPro-Config/global.ini`)
```ini
[Core]
UpdateInterval=10
ZoneWaitTimeout=5000
EnableLogging=true
LogLevel=Info
```

### AntiDupe Module (`NexusPro-Config/AntiDupe.ini`)
```ini
[Protection]
EnableCoordinateDupe=true
EnableTradeDupe=true
EnableAuctionDupe=true
EnableMailDupe=true
EnableBoxDupe=true
EnableQuestDupe=true
EnableMoneyDupe=true

[Timing]
TradeDelayMs=1000
AuctionDelayMs=2000
MailDelayMs=1500

[Thresholds]
MaxTradesPerMinute=10
MaxAuctionsPerMinute=5
MaxMailsPerMinute=8
```

## Module Development

### Creating a New Module

1. Create a new folder under `Modules/` (e.g., `Modules/AntiCheat/`)
2. Implement the `IModule` interface:

```cpp
class AntiCheatModule : public IModule
{
public:
    // IModule implementation
    bool Initialize() override;
    bool Start() override;
    void Stop() override;
    void Shutdown() override;
    
    std::string GetName() const override { return "AntiCheat"; }
    std::string GetVersion() const override { return "1.0.0"; }
    std::string GetDescription() const override { return "Anti-cheat protection"; }
    
    bool LoadConfig(const std::string& configPath) override;
    bool SaveConfig(const std::string& configPath) override;
    
    void Update() override;
    void OnZoneStart() override;
};
```

3. Register the module in `ModuleManager::CreateModuleInstances()`:

```cpp
RegisterModule(std::make_shared<Modules::AntiCheatModule>());
```

### Module Lifecycle

1. **Initialize**: Load configuration, prepare resources
2. **Start**: Install hooks, activate protection
3. **Update**: Called periodically during runtime
4. **OnZoneStart**: Called when zone server starts
5. **Stop**: Remove hooks, deactivate protection
6. **Shutdown**: Clean up resources

## Logging

The logging system provides multiple levels:
- **Debug**: Detailed debugging information
- **Info**: General information messages
- **Warning**: Warning messages for non-critical issues
- **Error**: Error messages for critical issues

Logs are written to both console and file (`NexusPro-Config/logs/`).

## Thread Safety

All modules and core systems are designed to be thread-safe:
- Configuration access is protected by mutexes
- Module state changes are atomic
- Player tracking data is protected by locks

## Performance

- **Low Overhead**: Minimal impact on server performance
- **Efficient Updates**: 10ms update interval by default
- **Smart Cleanup**: Automatic cleanup of old tracking data
- **Rate Limiting**: Built-in rate limiting to prevent spam

## Compatibility

- **Target Platform**: Windows x64
- **Visual Studio**: 2022 (v143 toolset)
- **C++ Standard**: C++17
- **RFOnline Version**: ******* (adaptable to other versions)

## Building

### Requirements
- Visual Studio 2022 with C++ development tools
- Windows 10 SDK
- x64 build tools

### Build Steps
1. Open `NexusPro.sln`
2. Select Release x64 configuration
3. Build Solution (Ctrl+Shift+B)
4. Output: `bin/Release/NexusProtection.dll`

## License

This project is licensed under the MIT License - see the original Yorozuya project for reference.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your module or enhancement
4. Test thoroughly
5. Submit a pull request

## Support

For issues, questions, or contributions, please refer to the project documentation or create an issue in the repository.

---

**Note**: This is a modernized version of the original Yorozuya project, redesigned for better maintainability, performance, and ease of use.
