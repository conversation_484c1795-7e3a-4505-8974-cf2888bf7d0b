# NexusPro Deployment Guide

## Overview

NexusPro is designed for easy deployment as a single DLL that integrates with your RFOnline ZoneServer. This guide covers installation, configuration, and troubleshooting.

## Deployment Methods

### Method 1: DLL Injection (Recommended)

This is the safest and most flexible deployment method.

1. **Copy DLL**:
   ```
   Copy NexusProtection.dll to the same directory as ZoneServer.exe
   ```

2. **Create Injector** (optional):
   ```cpp
   // Simple DLL injector
   HMODULE hMod = LoadLibrary(L"NexusProtection.dll");
   if (hMod) {
       // DLL loaded successfully
   }
   ```

3. **Auto-Load Setup**:
   - The DLL will automatically initialize when loaded
   - Configuration folders will be created automatically

### Method 2: Manual Loading

For servers that require manual control over loading:

1. **Place DLL**: Copy to ZoneServer directory
2. **Load via Code**: Use LoadLibrary in your server code
3. **Call Exports**: Use exported functions for control

```cpp
// Load the DLL
HMODULE hNexus = LoadLibrary(L"NexusProtection.dll");

// Get function pointers
typedef BOOL (*InitFunc)();
typedef BOOL (*ShutdownFunc)();

InitFunc InitNexus = (InitFunc)GetProcAddress(hNexus, "InitializeNexusProtection");
ShutdownFunc ShutdownNexus = (ShutdownFunc)GetProcAddress(hNexus, "ShutdownNexusProtection");

// Initialize
if (InitNexus) {
    InitNexus();
}
```

## Directory Structure

After deployment, your server directory should look like this:

```
ZoneServer/
├── ZoneServer.exe
├── NexusProtection.dll
├── NexusPro-Config/
│   ├── global.ini
│   ├── AntiDupe.ini
│   ├── logs/
│   │   └── NexusProtection_YYYYMMDD_HHMMSS.log
│   └── [other module configs]
└── [other server files]
```

## Configuration

### Initial Setup

1. **First Run**: Start ZoneServer with NexusProtection.dll
2. **Auto-Creation**: Configuration files are created automatically
3. **Customize**: Edit .ini files in NexusPro-Config/ as needed
4. **Restart**: Restart server to apply changes

### Global Configuration

Edit `NexusPro-Config/global.ini`:

```ini
[Core]
UpdateInterval=10          # Module update frequency (ms)
ZoneWaitTimeout=5000      # Wait time for zone start (ms)
EnableLogging=true        # Enable logging system
LogLevel=Info            # Debug, Info, Warning, Error

[Performance]
MaxThreads=4             # Maximum processing threads
CleanupInterval=5        # Memory cleanup interval (minutes)
```

### Module Configuration

Each module has its own configuration file:

- `AntiDupe.ini` - Anti-duplication settings
- `AntiCheat.ini` - Anti-cheat settings (when implemented)
- `AntiSpeedHack.ini` - Speed hack protection (when implemented)

### Configuration Hot-Reload

Some settings can be reloaded without restart:
- Log levels
- Rate limiting thresholds
- Detection sensitivity

To reload: Send SIGUSR1 to the process (if supported) or restart the server.

## Integration Points

### Zone Server Hooks

NexusPro automatically hooks into these server functions:
- Player movement validation
- Trade system
- Auction system
- Mail system
- Quest completion
- Item operations

### Custom Integration

For custom server modifications:

```cpp
// Notify NexusPro of zone start
extern "C" void NotifyZoneServerStart();

// In your zone initialization code:
NotifyZoneServerStart();
```

## Monitoring and Logging

### Log Files

Logs are written to `NexusPro-Config/logs/`:
- Format: `NexusProtection_YYYYMMDD_HHMMSS.log`
- Rotation: New file daily
- Levels: Debug, Info, Warning, Error

### Log Analysis

Common log patterns to monitor:

```
[INFO] NexusCore initialized successfully
[INFO] AntiDupe module started successfully
[WARNING] Trade rate limit exceeded for player 12345
[ERROR] Failed to install AntiDupe hooks
```

### Performance Monitoring

Monitor these metrics:
- Module initialization time
- Hook installation success
- Player tracking memory usage
- Update loop performance

## Troubleshooting

### Common Issues

1. **DLL Not Loading**:
   ```
   Symptoms: No log files created, no protection active
   Solutions:
   - Check DLL dependencies with Dependency Walker
   - Verify x64 architecture match
   - Check Windows Defender/antivirus exclusions
   - Ensure Visual C++ Redistributable is installed
   ```

2. **Configuration Not Loading**:
   ```
   Symptoms: Default settings used, no custom config applied
   Solutions:
   - Check file permissions on NexusPro-Config/
   - Verify .ini file format (no BOM, correct encoding)
   - Check for syntax errors in configuration files
   ```

3. **Hooks Not Working**:
   ```
   Symptoms: Protection not active, exploits still possible
   Solutions:
   - Verify server version compatibility
   - Check for conflicting DLLs
   - Review hook installation logs
   - Ensure proper server function addresses
   ```

4. **Performance Issues**:
   ```
   Symptoms: Server lag, high CPU usage
   Solutions:
   - Increase UpdateInterval in global.ini
   - Reduce logging verbosity
   - Optimize module configurations
   - Check for memory leaks in logs
   ```

### Debug Mode

Enable debug mode for troubleshooting:

```ini
[Core]
LogLevel=Debug
EnableDebugMode=true
```

This provides detailed information about:
- Module loading process
- Hook installation
- Player tracking
- Configuration parsing

### Compatibility Testing

Test compatibility with your server:

1. **Backup**: Always backup your server before deployment
2. **Test Environment**: Deploy to test server first
3. **Gradual Rollout**: Enable modules one by one
4. **Monitor**: Watch logs and performance metrics
5. **Rollback Plan**: Keep previous server version ready

## Security Considerations

### File Permissions

Secure the configuration directory:
```
NexusPro-Config/ - Read/Write for server process only
logs/ - Write access for server process
*.ini - Read access for server process
```

### Network Security

NexusPro doesn't open network ports, but consider:
- Firewall rules for your server
- Access control for configuration files
- Log file security (may contain player data)

### Update Security

When updating NexusPro:
1. Stop the server
2. Backup current DLL and configs
3. Replace DLL
4. Update configs if needed
5. Test in safe environment
6. Deploy to production

## Performance Optimization

### Server-Side Optimizations

1. **CPU Affinity**: Bind server to specific CPU cores
2. **Memory**: Ensure adequate RAM for player tracking
3. **Storage**: Use SSD for log files and configs
4. **Network**: Optimize network settings for your player count

### NexusPro Optimizations

1. **Update Interval**: Increase for lower CPU usage
2. **Cleanup Frequency**: Adjust based on player count
3. **Logging**: Reduce verbosity in production
4. **Module Selection**: Disable unused modules

### Monitoring Commands

Use these to monitor NexusPro performance:

```cpp
// Check if NexusPro is running
extern "C" BOOL IsNexusProtectionRunning();

// Get module status (if implemented)
extern "C" int GetModuleStatus(const char* moduleName);
```

## Backup and Recovery

### Backup Strategy

Regular backups should include:
- NexusProtection.dll
- Complete NexusPro-Config/ directory
- Server configuration files
- Recent log files

### Recovery Procedure

If issues occur:
1. Stop the server
2. Remove NexusProtection.dll
3. Start server without protection (temporary)
4. Investigate logs
5. Fix configuration or update DLL
6. Redeploy with fixes

## Support and Maintenance

### Regular Maintenance

- Monitor log files for errors
- Update configurations as needed
- Clean old log files periodically
- Update NexusPro when new versions available

### Getting Help

When reporting issues, include:
- Server version and configuration
- NexusPro version
- Relevant log excerpts
- Steps to reproduce the issue
- System specifications

This completes the deployment guide for NexusPro. Follow these steps for a successful deployment to your RFOnline server.
