# NexusPro Build Instructions

## Prerequisites

### Required Software
1. **Visual Studio 2022** (Community, Professional, or Enterprise)
   - Install with "Desktop development with C++" workload
   - Ensure Windows 10/11 SDK is installed
   - Make sure x64 build tools are available

2. **Windows 10 SDK** (if not included with Visual Studio)
   - Minimum version: 10.0.19041.0
   - Can be installed via Visual Studio Installer

### System Requirements
- Windows 10/11 x64
- At least 4GB RAM for building
- 2GB free disk space

## Building the Project

### Method 1: Visual Studio IDE

1. **Open the Solution**:
   ```
   Open Visual Studio 2022
   File → Open → Project/Solution
   Navigate to NexusProtection/NexusPro.sln
   ```

2. **Configure Build Settings**:
   ```
   Select "Release" configuration
   Select "x64" platform
   ```

3. **Build the Project**:
   ```
   Build → Build Solution (Ctrl+Shift+B)
   Or right-click solution → Build Solution
   ```

4. **Locate Output**:
   ```
   Output file: NexusProtection/bin/Release/NexusProtection.dll
   ```

### Method 2: Command Line (MSBuild)

1. **Open Developer Command Prompt**:
   ```
   Start Menu → Visual Studio 2022 → Developer Command Prompt for VS 2022
   ```

2. **Navigate to Project Directory**:
   ```cmd
   cd "path\to\NexusProtection"
   ```

3. **Build the Project**:
   ```cmd
   msbuild NexusPro.sln /p:Configuration=Release /p:Platform=x64
   ```

### Method 3: PowerShell Script

1. **Create Build Script** (`build.ps1`):
   ```powershell
   # Set build parameters
   $SolutionPath = "NexusPro.sln"
   $Configuration = "Release"
   $Platform = "x64"
   
   # Find MSBuild
   $MSBuildPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\*\MSBuild\Current\Bin\MSBuild.exe"
   $MSBuild = Get-ChildItem -Path $MSBuildPath | Select-Object -First 1
   
   if (-not $MSBuild) {
       Write-Error "MSBuild not found. Please install Visual Studio 2022."
       exit 1
   }
   
   # Build the solution
   Write-Host "Building NexusPro..." -ForegroundColor Green
   & $MSBuild.FullName $SolutionPath /p:Configuration=$Configuration /p:Platform=$Platform /m
   
   if ($LASTEXITCODE -eq 0) {
       Write-Host "Build completed successfully!" -ForegroundColor Green
       Write-Host "Output: bin\$Configuration\NexusProtection.dll" -ForegroundColor Yellow
   } else {
       Write-Error "Build failed with exit code $LASTEXITCODE"
       exit $LASTEXITCODE
   }
   ```

2. **Run the Script**:
   ```powershell
   .\build.ps1
   ```

## Build Configurations

### Debug Configuration
- **Purpose**: Development and debugging
- **Optimizations**: Disabled
- **Debug Info**: Full
- **Output**: `bin/Debug/NexusProtection.dll`

### Release Configuration
- **Purpose**: Production deployment
- **Optimizations**: Enabled
- **Debug Info**: Minimal
- **Output**: `bin/Release/NexusProtection.dll`

## Troubleshooting

### Common Build Errors

1. **"Windows.h not found"**:
   - Install Windows 10/11 SDK
   - Verify SDK path in project properties

2. **"MSB8036: The Windows SDK version X was not found"**:
   - Update Windows SDK version in project properties
   - Or install the required SDK version

3. **"LNK1104: cannot open file 'kernel32.lib'"**:
   - Verify x64 build tools are installed
   - Check platform toolset (should be v143)

4. **"C1083: Cannot open include file: 'pch.h'"**:
   - Ensure precompiled headers are enabled
   - Verify pch.cpp has "Create" precompiled header setting

### Build Performance Tips

1. **Enable Parallel Builds**:
   ```
   Tools → Options → Projects and Solutions → Build and Run
   Set "maximum number of parallel project builds" to CPU core count
   ```

2. **Use RAM Disk** (optional):
   - Move temp/obj directories to RAM disk for faster builds
   - Configure via project properties

3. **Exclude from Antivirus**:
   - Add project directory to antivirus exclusions
   - Significantly improves build speed

## Deployment

### Manual Deployment

1. **Copy Files**:
   ```
   Copy bin/Release/NexusProtection.dll to ZoneServer directory
   ```

2. **Configuration**:
   ```
   NexusProtection.dll will auto-create NexusPro-Config/ folder
   Copy sample configs from SampleConfigs/ if needed
   ```

### Automated Deployment Script

Create `deploy.bat`:
```batch
@echo off
set SERVER_PATH=C:\RFServer\ZoneServer
set BUILD_PATH=bin\Release

echo Deploying NexusProtection...
copy "%BUILD_PATH%\NexusProtection.dll" "%SERVER_PATH%\" /Y

if exist "SampleConfigs" (
    if not exist "%SERVER_PATH%\NexusPro-Config" (
        mkdir "%SERVER_PATH%\NexusPro-Config"
        copy "SampleConfigs\*" "%SERVER_PATH%\NexusPro-Config\" /Y
        echo Sample configurations copied.
    )
)

echo Deployment complete!
pause
```

## Verification

### Test Build Success
1. **Check File Size**: NexusProtection.dll should be > 100KB
2. **Dependency Check**: Use `dumpbin /dependents NexusProtection.dll`
3. **Symbol Check**: Verify exports with `dumpbin /exports NexusProtection.dll`

### Test Runtime
1. **Place DLL**: Copy to ZoneServer directory
2. **Start Server**: Launch ZoneServer.exe
3. **Check Logs**: Look for NexusProtection initialization messages
4. **Verify Config**: Check that NexusPro-Config folder is created

## Advanced Build Options

### Custom Preprocessor Definitions
Add to project properties → C/C++ → Preprocessor:
```
NEXUS_DEBUG_MODE=1          # Enable debug features
NEXUS_PERFORMANCE_MODE=1    # Enable performance optimizations
NEXUS_CUSTOM_LOGGING=1      # Enable custom logging features
```

### Link-Time Code Generation (LTCG)
For maximum performance in Release builds:
```
Project Properties → C/C++ → Optimization → Whole Program Optimization: Yes
Project Properties → Linker → Optimization → Link Time Code Generation: Use Link Time Code Generation
```

### Profile-Guided Optimization (PGO)
For production builds with performance profiling:
1. Build with PGO instrumentation
2. Run typical server workload
3. Rebuild with PGO optimization

This completes the build instructions for NexusPro. The resulting DLL should be ready for deployment to your RFOnline server.
