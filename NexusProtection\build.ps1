# NexusPro Build Script
# This script builds the NexusProtection DLL using MSBuild

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [switch]$Clean,
    [switch]$Rebuild,
    [switch]$Deploy,
    [string]$DeployPath = ""
)

# Script configuration
$SolutionFile = "NexusPro.sln"
$ProjectName = "NexusProtection"
$OutputDll = "NexusProtection.dll"

# Colors for output
$ColorInfo = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"
$ColorSuccess = "Cyan"

function Write-Info($message) {
    Write-Host $message -ForegroundColor $ColorInfo
}

function Write-Warning($message) {
    Write-Host $message -ForegroundColor $ColorWarning
}

function Write-Error($message) {
    Write-Host $message -ForegroundColor $ColorError
}

function Write-Success($message) {
    Write-Host $message -ForegroundColor $ColorSuccess
}

function Find-MSBuild {
    # Try to find MSBuild in common locations
    $MSBuildPaths = @(
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    foreach ($path in $MSBuildPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    
    # Try to find via vswhere
    $vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vswhere) {
        $vsPath = & $vswhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
        if ($vsPath) {
            $msbuildPath = Join-Path $vsPath "MSBuild\Current\Bin\MSBuild.exe"
            if (Test-Path $msbuildPath) {
                return $msbuildPath
            }
        }
    }
    
    return $null
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if solution file exists
    if (-not (Test-Path $SolutionFile)) {
        Write-Error "Solution file '$SolutionFile' not found!"
        return $false
    }
    
    # Find MSBuild
    $script:MSBuildPath = Find-MSBuild
    if (-not $script:MSBuildPath) {
        Write-Error "MSBuild not found! Please install Visual Studio 2022 or 2019."
        return $false
    }
    
    Write-Info "Found MSBuild: $script:MSBuildPath"
    return $true
}

function Invoke-Build {
    param([string]$Target = "Build")
    
    $buildArgs = @(
        $SolutionFile,
        "/p:Configuration=$Configuration",
        "/p:Platform=$Platform",
        "/m",  # Multi-processor build
        "/v:minimal"  # Minimal verbosity
    )
    
    if ($Target -eq "Rebuild") {
        $buildArgs += "/t:Rebuild"
    } elseif ($Target -eq "Clean") {
        $buildArgs += "/t:Clean"
    }
    
    Write-Info "Running: $Target ($Configuration|$Platform)"
    Write-Info "Command: MSBuild $($buildArgs -join ' ')"
    
    $process = Start-Process -FilePath $script:MSBuildPath -ArgumentList $buildArgs -Wait -PassThru -NoNewWindow
    
    return $process.ExitCode -eq 0
}

function Test-BuildOutput {
    $outputPath = "bin\$Configuration\$OutputDll"
    
    if (-not (Test-Path $outputPath)) {
        Write-Error "Build output not found: $outputPath"
        return $false
    }
    
    $fileInfo = Get-Item $outputPath
    Write-Success "Build successful!"
    Write-Info "Output: $outputPath"
    Write-Info "Size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB"
    Write-Info "Modified: $($fileInfo.LastWriteTime)"
    
    return $true
}

function Invoke-Deploy {
    if (-not $DeployPath) {
        Write-Warning "No deploy path specified. Use -DeployPath parameter."
        return $false
    }
    
    if (-not (Test-Path $DeployPath)) {
        Write-Error "Deploy path does not exist: $DeployPath"
        return $false
    }
    
    $sourcePath = "bin\$Configuration\$OutputDll"
    $destPath = Join-Path $DeployPath $OutputDll
    
    try {
        Copy-Item $sourcePath $destPath -Force
        Write-Success "Deployed to: $destPath"
        
        # Copy sample configs if NexusPro-Config doesn't exist
        $configPath = Join-Path $DeployPath "NexusPro-Config"
        if (-not (Test-Path $configPath) -and (Test-Path "SampleConfigs")) {
            New-Item -ItemType Directory -Path $configPath -Force | Out-Null
            Copy-Item "SampleConfigs\*" $configPath -Force
            Write-Info "Sample configurations copied to: $configPath"
        }
        
        return $true
    }
    catch {
        Write-Error "Failed to deploy: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
Write-Info "=== NexusPro Build Script ==="
Write-Info "Configuration: $Configuration"
Write-Info "Platform: $Platform"

if (-not (Test-Prerequisites)) {
    exit 1
}

$success = $true

# Clean if requested
if ($Clean -or $Rebuild) {
    if (-not (Invoke-Build -Target "Clean")) {
        Write-Error "Clean failed!"
        $success = $false
    }
}

# Build
if ($success) {
    $target = if ($Rebuild) { "Rebuild" } else { "Build" }
    if (-not (Invoke-Build -Target $target)) {
        Write-Error "Build failed!"
        $success = $false
    }
}

# Verify output
if ($success) {
    if (-not (Test-BuildOutput)) {
        $success = $false
    }
}

# Deploy if requested and build was successful
if ($success -and $Deploy) {
    if (-not (Invoke-Deploy)) {
        $success = $false
    }
}

# Final result
if ($success) {
    Write-Success "=== Build completed successfully! ==="
} else {
    Write-Error "=== Build failed! ==="
    exit 1
}

# Usage examples
Write-Info ""
Write-Info "Usage examples:"
Write-Info "  .\build.ps1                                    # Build Release x64"
Write-Info "  .\build.ps1 -Configuration Debug               # Build Debug x64"
Write-Info "  .\build.ps1 -Clean                            # Clean and build"
Write-Info "  .\build.ps1 -Rebuild                          # Rebuild all"
Write-Info "  .\build.ps1 -Deploy -DeployPath C:\RFServer   # Build and deploy"
