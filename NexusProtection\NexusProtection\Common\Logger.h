#pragma once
#include "../pch.h"
#include "Singleton.h"

namespace NexusProtection
{
    enum class LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    };

    class Logger : public Singleton<Logger>
    {
    public:
        void Initialize(const std::string& logPath = "NexusPro-Config/logs/");
        void Shutdown();

        void LogDebug(const std::string& message);
        void LogInfo(const std::string& message);
        void LogWarning(const std::string& message);
        void LogError(const std::string& message);

        void SetLogLevel(LogLevel level) { m_logLevel = level; }
        void SetConsoleOutput(bool enabled) { m_consoleOutput = enabled; }
        void SetFileOutput(bool enabled) { m_fileOutput = enabled; }

    private:
        friend class Singleton<Logger>;
        Logger() = default;
        ~Logger() = default;

        void WriteLog(LogLevel level, const std::string& message);
        std::string GetTimestamp() const;
        std::string LogLevelToString(LogLevel level) const;

        LogLevel m_logLevel = LogLevel::Info;
        bool m_consoleOutput = true;
        bool m_fileOutput = true;
        bool m_initialized = false;
        
        std::string m_logPath;
        std::mutex m_logMutex;
        std::ofstream m_logFile;
    };
}
