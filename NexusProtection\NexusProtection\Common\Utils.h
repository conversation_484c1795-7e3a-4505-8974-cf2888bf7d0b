#pragma once
#include "../pch.h"

namespace NexusProtection
{
    class Utils
    {
    public:
        // File and directory operations
        static bool FileExists(const std::string& filePath);
        static bool DirectoryExists(const std::string& dirPath);
        static bool CreateDirectoryRecursive(const std::string& dirPath);
        static std::string GetCurrentDirectory();
        static std::string GetModuleDirectory();
        
        // String operations
        static std::string ToLower(const std::string& str);
        static std::string ToUpper(const std::string& str);
        static std::string Trim(const std::string& str);
        static std::vector<std::string> Split(const std::string& str, char delimiter);
        static std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);
        
        // Time operations
        static std::string GetCurrentDateTimeString();
        static std::string GetCurrentDateString();
        static std::string GetCurrentTimeString();
        static uint64_t GetCurrentTimestamp();
        
        // Conversion utilities
        static std::wstring StringToWString(const std::string& str);
        static std::string WStringToString(const std::wstring& wstr);
        
        // Memory operations
        static bool WriteMemory(void* address, const void* data, size_t size);
        static bool ReadMemory(const void* address, void* buffer, size_t size);
        static bool ProtectMemory(void* address, size_t size, DWORD protection, DWORD* oldProtection = nullptr);
        
        // Process operations
        static HMODULE GetCurrentModule();
        static std::string GetModulePath(HMODULE module = nullptr);
        static DWORD GetProcessId();
        static HANDLE GetCurrentProcessHandle();
        
        // Configuration helpers
        static std::string GetConfigPath(const std::string& moduleName);
        static std::string GetLogPath();
    };
}
