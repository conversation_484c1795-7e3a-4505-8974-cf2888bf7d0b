#pragma once
#include "../pch.h"
#include "Singleton.h"

namespace NexusProtection
{
    class ConfigManager : public Singleton<ConfigManager>
    {
    public:
        void Initialize();
        void Shutdown();

        // INI file operations
        bool LoadConfig(const std::string& filePath);
        bool SaveConfig(const std::string& filePath);
        
        // Value getters
        std::string GetString(const std::string& section, const std::string& key, const std::string& defaultValue = "");
        int GetInt(const std::string& section, const std::string& key, int defaultValue = 0);
        float GetFloat(const std::string& section, const std::string& key, float defaultValue = 0.0f);
        bool GetBool(const std::string& section, const std::string& key, bool defaultValue = false);
        
        // Value setters
        void SetString(const std::string& section, const std::string& key, const std::string& value);
        void SetInt(const std::string& section, const std::string& key, int value);
        void SetFloat(const std::string& section, const std::string& key, float value);
        void SetBool(const std::string& section, const std::string& key, bool value);
        
        // Section operations
        bool HasSection(const std::string& section) const;
        bool HasKey(const std::string& section, const std::string& key) const;
        std::vector<std::string> GetSections() const;
        std::vector<std::string> GetKeys(const std::string& section) const;
        
        // Utility functions
        void Clear();
        void RemoveSection(const std::string& section);
        void RemoveKey(const std::string& section, const std::string& key);

    private:
        friend class Singleton<ConfigManager>;
        ConfigManager() = default;
        ~ConfigManager() = default;

        struct ConfigData
        {
            std::map<std::string, std::map<std::string, std::string>> sections;
        };

        ConfigData m_config;
        std::mutex m_configMutex;
        bool m_initialized = false;

        std::string TrimString(const std::string& str) const;
        std::pair<std::string, std::string> ParseKeyValue(const std::string& line) const;
    };
}
