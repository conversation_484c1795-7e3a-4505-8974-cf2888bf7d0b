#include "../pch.h"
#include "Utils.h"
#include <filesystem>
#include <iomanip>

namespace NexusProtection
{
    bool Utils::FileExists(const std::string& filePath)
    {
        return std::filesystem::exists(filePath);
    }

    bool Utils::DirectoryExists(const std::string& dirPath)
    {
        return std::filesystem::exists(dirPath) && std::filesystem::is_directory(dirPath);
    }

    bool Utils::CreateDirectoryRecursive(const std::string& dirPath)
    {
        try
        {
            return std::filesystem::create_directories(dirPath);
        }
        catch (const std::exception&)
        {
            return false;
        }
    }

    std::string Utils::GetCurrentDirectory()
    {
        try
        {
            return std::filesystem::current_path().string();
        }
        catch (const std::exception&)
        {
            return "";
        }
    }

    std::string Utils::GetModuleDirectory()
    {
        char path[MAX_PATH];
        HMODULE hModule = GetCurrentModule();
        if (GetModuleFileNameA(hModule, path, MAX_PATH))
        {
            std::string modulePath(path);
            size_t lastSlash = modulePath.find_last_of("\\/");
            if (lastSlash != std::string::npos)
            {
                return modulePath.substr(0, lastSlash + 1);
            }
        }
        return GetCurrentDirectory();
    }

    std::string Utils::ToLower(const std::string& str)
    {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), ::tolower);
        return result;
    }

    std::string Utils::ToUpper(const std::string& str)
    {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), ::toupper);
        return result;
    }

    std::string Utils::Trim(const std::string& str)
    {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    std::vector<std::string> Utils::Split(const std::string& str, char delimiter)
    {
        std::vector<std::string> result;
        std::stringstream ss(str);
        std::string item;
        
        while (std::getline(ss, item, delimiter))
        {
            result.push_back(item);
        }
        
        return result;
    }

    std::string Utils::Join(const std::vector<std::string>& strings, const std::string& delimiter)
    {
        if (strings.empty())
            return "";
        
        std::string result = strings[0];
        for (size_t i = 1; i < strings.size(); ++i)
        {
            result += delimiter + strings[i];
        }
        
        return result;
    }

    std::string Utils::GetCurrentDateTimeString()
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
        return ss.str();
    }

    std::string Utils::GetCurrentDateString()
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d");
        return ss.str();
    }

    std::string Utils::GetCurrentTimeString()
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
        return ss.str();
    }

    uint64_t Utils::GetCurrentTimestamp()
    {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    std::wstring Utils::StringToWString(const std::string& str)
    {
        if (str.empty())
            return std::wstring();
        
        int size = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
        std::wstring result(size, 0);
        MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], size);
        return result;
    }

    std::string Utils::WStringToString(const std::wstring& wstr)
    {
        if (wstr.empty())
            return std::string();
        
        int size = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
        std::string result(size, 0);
        WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], size, nullptr, nullptr);
        return result;
    }

    bool Utils::WriteMemory(void* address, const void* data, size_t size)
    {
        DWORD oldProtection;
        if (!VirtualProtect(address, size, PAGE_EXECUTE_READWRITE, &oldProtection))
            return false;
        
        memcpy(address, data, size);
        
        VirtualProtect(address, size, oldProtection, &oldProtection);
        return true;
    }

    bool Utils::ReadMemory(const void* address, void* buffer, size_t size)
    {
        try
        {
            memcpy(buffer, address, size);
            return true;
        }
        catch (...)
        {
            return false;
        }
    }

    bool Utils::ProtectMemory(void* address, size_t size, DWORD protection, DWORD* oldProtection)
    {
        DWORD temp;
        return VirtualProtect(address, size, protection, oldProtection ? oldProtection : &temp);
    }

    HMODULE Utils::GetCurrentModule()
    {
        HMODULE hModule = nullptr;
        GetModuleHandleExA(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS |
                          GET_MODULE_HANDLE_EX_FLAG_UNCHANGED_REFCOUNT,
                          reinterpret_cast<LPCSTR>(&GetCurrentModule),
                          &hModule);
        return hModule;
    }

    std::string Utils::GetModulePath(HMODULE module)
    {
        char path[MAX_PATH];
        if (GetModuleFileNameA(module ? module : GetCurrentModule(), path, MAX_PATH))
        {
            return std::string(path);
        }
        return "";
    }

    DWORD Utils::GetProcessId()
    {
        return GetCurrentProcessId();
    }

    HANDLE Utils::GetCurrentProcessHandle()
    {
        return GetCurrentProcess();
    }

    std::string Utils::GetConfigPath(const std::string& moduleName)
    {
        return GetModuleDirectory() + "NexusPro-Config/" + moduleName + ".ini";
    }

    std::string Utils::GetLogPath()
    {
        return GetModuleDirectory() + "NexusPro-Config/logs/";
    }
}
