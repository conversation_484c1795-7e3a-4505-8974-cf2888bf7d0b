# NexusPro Global Configuration
# This file contains core system settings that affect all modules

[Core]
# Update interval for module processing (milliseconds)
UpdateInterval=10

# Timeout to wait for zone server start (milliseconds)
ZoneWaitTimeout=5000

# Enable logging system
EnableLogging=true

# Log level: Debug, Info, Warning, Error
LogLevel=Info

# Enable console output for logs
ConsoleOutput=true

# Enable file output for logs
FileOutput=true

[Performance]
# Maximum number of threads for module processing
MaxThreads=4

# Memory cleanup interval (minutes)
CleanupInterval=5

# Player tracking timeout (minutes)
PlayerTrackingTimeout=10
