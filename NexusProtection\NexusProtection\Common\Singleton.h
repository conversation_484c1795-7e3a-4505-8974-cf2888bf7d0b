#pragma once
#include "../pch.h"

namespace NexusProtection
{
    template<typename T>
    class Singleton
    {
    public:
        static T& GetInstance()
        {
            static T instance;
            return instance;
        }

        // Delete copy constructor and assignment operator
        Singleton(const Singleton&) = delete;
        Singleton& operator=(const Singleton&) = delete;

    protected:
        Singleton() = default;
        virtual ~Singleton() = default;
    };
}
