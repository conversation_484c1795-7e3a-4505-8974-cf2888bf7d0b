#pragma once
#include "../../pch.h"
#include "../../Common/IModule.h"

namespace NexusProtection
{
    namespace Modules
    {
        class AntiDupeModule : public IModule
        {
        public:
            AntiDupeModule();
            virtual ~AntiDupeModule() = default;

            // IModule implementation
            bool Initialize() override;
            bool Start() override;
            void Stop() override;
            void Shutdown() override;

            std::string GetName() const override { return "AntiDupe"; }
            std::string GetVersion() const override { return "1.0.0"; }
            std::string GetDescription() const override { return "Prevents item duplication exploits"; }

            bool LoadConfig(const std::string& configPath) override;
            bool SaveConfig(const std::string& configPath) override;

            void Update() override;
            void OnZoneStart() override;

        private:
            // Configuration settings
            bool m_enableCoordinateDupeProtection = true;
            bool m_enableTradeDupeProtection = true;
            bool m_enableAuctionDupeProtection = true;
            bool m_enableMailDupeProtection = true;
            bool m_enableBoxDupeProtection = true;
            bool m_enableQuestDupeProtection = true;
            bool m_enableMoneyDupeProtection = true;
            
            // Timing settings
            int m_tradeDelayMs = 1000;
            int m_auctionDelayMs = 2000;
            int m_mailDelayMs = 1500;
            
            // Detection thresholds
            int m_maxTradesPerMinute = 10;
            int m_maxAuctionsPerMinute = 5;
            int m_maxMailsPerMinute = 8;
            
            // Internal state
            bool m_hooksInstalled = false;
            std::mutex m_stateMutex;
            
            // Player tracking
            struct PlayerTrackingData
            {
                uint64_t lastTradeTime = 0;
                uint64_t lastAuctionTime = 0;
                uint64_t lastMailTime = 0;
                int tradesThisMinute = 0;
                int auctionsThisMinute = 0;
                int mailsThisMinute = 0;
                uint64_t minuteStartTime = 0;
            };
            
            std::unordered_map<uint32_t, PlayerTrackingData> m_playerTracking;
            std::mutex m_trackingMutex;
            
            // Hook management
            bool InstallHooks();
            void RemoveHooks();
            
            // Configuration helpers
            void LoadDefaultConfig();
            void ApplyConfig();
            
            // Anti-dupe implementations
            bool ValidateTradeRequest(uint32_t playerId);
            bool ValidateAuctionRequest(uint32_t playerId);
            bool ValidateMailRequest(uint32_t playerId);
            bool ValidateCoordinateChange(uint32_t playerId, float x, float y, float z);
            
            // Player tracking helpers
            PlayerTrackingData& GetPlayerTracking(uint32_t playerId);
            void UpdatePlayerTracking(uint32_t playerId);
            void CleanupOldTrackingData();
            
            // Utility functions
            uint64_t GetCurrentTimeMs() const;
            bool IsRateLimited(uint64_t lastTime, int delayMs) const;
            void ResetMinuteCounters(PlayerTrackingData& data);
            
            // Hook functions (static for C-style callbacks)
            static bool OnPlayerTrade(uint32_t playerId, void* tradeData);
            static bool OnPlayerAuction(uint32_t playerId, void* auctionData);
            static bool OnPlayerMail(uint32_t playerId, void* mailData);
            static bool OnPlayerMove(uint32_t playerId, float x, float y, float z);
            static bool OnQuestComplete(uint32_t playerId, uint32_t questId);
            static bool OnMoneyTransaction(uint32_t playerId, int64_t amount);
        };
    }
}
