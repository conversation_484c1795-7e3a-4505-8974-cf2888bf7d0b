#include "../pch.h"
#include "ConfigManager.h"
#include "Utils.h"

namespace NexusProtection
{
    void ConfigManager::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        if (!m_initialized)
        {
            m_initialized = true;
        }
    }

    void ConfigManager::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        if (m_initialized)
        {
            Clear();
            m_initialized = false;
        }
    }

    bool ConfigManager::LoadConfig(const std::string& filePath)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        std::ifstream file(filePath);
        if (!file.is_open())
        {
            return false;
        }

        std::string line;
        std::string currentSection;
        
        while (std::getline(file, line))
        {
            line = TrimString(line);
            
            // Skip empty lines and comments
            if (line.empty() || line[0] == ';' || line[0] == '#')
                continue;
            
            // Check for section header
            if (line[0] == '[' && line.back() == ']')
            {
                currentSection = line.substr(1, line.length() - 2);
                currentSection = TrimString(currentSection);
                continue;
            }
            
            // Parse key-value pair
            auto keyValue = ParseKeyValue(line);
            if (!keyValue.first.empty())
            {
                m_config.sections[currentSection][keyValue.first] = keyValue.second;
            }
        }
        
        file.close();
        return true;
    }

    bool ConfigManager::SaveConfig(const std::string& filePath)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        // Create directory if it doesn't exist
        size_t lastSlash = filePath.find_last_of("\\/");
        if (lastSlash != std::string::npos)
        {
            std::string directory = filePath.substr(0, lastSlash);
            Utils::CreateDirectoryRecursive(directory);
        }
        
        std::ofstream file(filePath);
        if (!file.is_open())
        {
            return false;
        }

        for (const auto& section : m_config.sections)
        {
            if (!section.first.empty())
            {
                file << "[" << section.first << "]" << std::endl;
            }
            
            for (const auto& keyValue : section.second)
            {
                file << keyValue.first << "=" << keyValue.second << std::endl;
            }
            
            file << std::endl;
        }
        
        file.close();
        return true;
    }

    std::string ConfigManager::GetString(const std::string& section, const std::string& key, const std::string& defaultValue)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        auto sectionIt = m_config.sections.find(section);
        if (sectionIt != m_config.sections.end())
        {
            auto keyIt = sectionIt->second.find(key);
            if (keyIt != sectionIt->second.end())
            {
                return keyIt->second;
            }
        }
        
        return defaultValue;
    }

    int ConfigManager::GetInt(const std::string& section, const std::string& key, int defaultValue)
    {
        std::string value = GetString(section, key);
        if (value.empty())
            return defaultValue;
        
        try
        {
            return std::stoi(value);
        }
        catch (...)
        {
            return defaultValue;
        }
    }

    float ConfigManager::GetFloat(const std::string& section, const std::string& key, float defaultValue)
    {
        std::string value = GetString(section, key);
        if (value.empty())
            return defaultValue;
        
        try
        {
            return std::stof(value);
        }
        catch (...)
        {
            return defaultValue;
        }
    }

    bool ConfigManager::GetBool(const std::string& section, const std::string& key, bool defaultValue)
    {
        std::string value = Utils::ToLower(GetString(section, key));
        if (value.empty())
            return defaultValue;
        
        return (value == "true" || value == "1" || value == "yes" || value == "on");
    }

    void ConfigManager::SetString(const std::string& section, const std::string& key, const std::string& value)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_config.sections[section][key] = value;
    }

    void ConfigManager::SetInt(const std::string& section, const std::string& key, int value)
    {
        SetString(section, key, std::to_string(value));
    }

    void ConfigManager::SetFloat(const std::string& section, const std::string& key, float value)
    {
        SetString(section, key, std::to_string(value));
    }

    void ConfigManager::SetBool(const std::string& section, const std::string& key, bool value)
    {
        SetString(section, key, value ? "true" : "false");
    }

    bool ConfigManager::HasSection(const std::string& section) const
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        return m_config.sections.find(section) != m_config.sections.end();
    }

    bool ConfigManager::HasKey(const std::string& section, const std::string& key) const
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        auto sectionIt = m_config.sections.find(section);
        if (sectionIt != m_config.sections.end())
        {
            return sectionIt->second.find(key) != sectionIt->second.end();
        }
        
        return false;
    }

    std::vector<std::string> ConfigManager::GetSections() const
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        std::vector<std::string> sections;
        for (const auto& section : m_config.sections)
        {
            sections.push_back(section.first);
        }
        
        return sections;
    }

    std::vector<std::string> ConfigManager::GetKeys(const std::string& section) const
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        std::vector<std::string> keys;
        auto sectionIt = m_config.sections.find(section);
        if (sectionIt != m_config.sections.end())
        {
            for (const auto& keyValue : sectionIt->second)
            {
                keys.push_back(keyValue.first);
            }
        }
        
        return keys;
    }

    void ConfigManager::Clear()
    {
        m_config.sections.clear();
    }

    void ConfigManager::RemoveSection(const std::string& section)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_config.sections.erase(section);
    }

    void ConfigManager::RemoveKey(const std::string& section, const std::string& key)
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        
        auto sectionIt = m_config.sections.find(section);
        if (sectionIt != m_config.sections.end())
        {
            sectionIt->second.erase(key);
        }
    }

    std::string ConfigManager::TrimString(const std::string& str) const
    {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    std::pair<std::string, std::string> ConfigManager::ParseKeyValue(const std::string& line) const
    {
        size_t equalPos = line.find('=');
        if (equalPos == std::string::npos)
            return std::make_pair("", "");
        
        std::string key = TrimString(line.substr(0, equalPos));
        std::string value = TrimString(line.substr(equalPos + 1));
        
        return std::make_pair(key, value);
    }
}
