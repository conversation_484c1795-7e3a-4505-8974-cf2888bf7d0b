# AntiDupe Module Configuration
# This module prevents various item duplication exploits

[Protection]
# Enable coordinate duplication protection
EnableCoordinateDupe=true

# Enable trade duplication protection
EnableTradeDupe=true

# Enable auction duplication protection
EnableAuctionDupe=true

# Enable mail duplication protection
EnableMailDupe=true

# Enable box duplication protection
EnableBoxDupe=true

# Enable quest duplication protection
EnableQuestDupe=true

# Enable money duplication protection
EnableMoneyDupe=true

[Timing]
# Minimum delay between trades (milliseconds)
TradeDelayMs=1000

# Minimum delay between auction actions (milliseconds)
AuctionDelayMs=2000

# Minimum delay between mail actions (milliseconds)
MailDelayMs=1500

# Minimum delay between box operations (milliseconds)
BoxDelayMs=800

# Minimum delay between quest completions (milliseconds)
QuestDelayMs=5000

[Thresholds]
# Maximum trades per minute per player
MaxTradesPerMinute=10

# Maximum auction actions per minute per player
MaxAuctionsPerMinute=5

# Maximum mail actions per minute per player
MaxMailsPerMinute=8

# Maximum box operations per minute per player
MaxBoxOperationsPerMinute=15

# Maximum quest completions per minute per player
MaxQuestsPerMinute=3

[Coordinate]
# Maximum distance change per second (units)
MaxDistancePerSecond=1000.0

# Enable teleport detection
EnableTeleportDetection=true

# Maximum allowed teleport distance
MaxTeleportDistance=50.0

# Coordinate validation tolerance
CoordinateTolerance=5.0

[Logging]
# Log all blocked attempts
LogBlockedAttempts=true

# Log suspicious activity
LogSuspiciousActivity=true

# Log player statistics
LogPlayerStats=false
